# FranqAI Agents - Global .gitignore

# Environment files - CRITICAL: NEVER commit these
.env
.env.*
*.env
!.env.template

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# IDE and editors
.vscode/
.idea/
*.swp
*.swo
*~
.sublime-project
.sublime-workspace

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
log/

# Testing
.coverage
.pytest_cache/
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Documentation builds
docs/_build/
site/

# Temporary files
*.tmp
*.temp
.tmp/
temp/

# Generated Kubernetes files
k8s/deployment-*.yaml
!k8s/deployment-template.yaml

# Docker
.dockerignore.bak

# Secrets and keys (extra protection)
*key*
*secret*
*token*
*password*
*credential*
*.pem
*.key
*.crt
*.p12
*.pfx

# Backup files
*.bak
*.backup
*~

# Local configuration overrides
local.yaml
local.yml
local.json
config.local.*

# Service account files
service-account.json
gcloud-service-key.json
*.json
!package.json
!package-lock.json

# Terraform (if used)
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Helm (if used)
charts/*/charts/
charts/*/requirements.lock

# Node.js (if frontend is added)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Jupyter Notebooks
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# Poetry
poetry.lock

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
