# FranqAI Agents - Multi-Service AI Agent Platform

A scalable platform for deploying multiple AI agent services based on the LANGSERVE stack, integrated with Gemini AI Studio and deployed on Google Kubernetes Engine (GKE).

## 🏗️ Architecture Overview

This project provides a template-based approach for creating and deploying AI agent services:

- **Template Service**: `servico1` serves as the base template
- **Easy Duplication**: Copy and rename service folders to create new agents
- **Shared Stack**: All services use LANGSERVE + Gemini AI Studio
- **Secure Configuration**: API keys managed via environment variables and Kubernetes secrets
- **Automated CI/CD**: Bitbucket Pipelines for build and deployment
- **Path-based Routing**: Each service accessible at `/{service_name}`

## 🚀 Quick Start

### 1. Create a New Service

```bash
# Create a new service from template
./scripts/create-new-service.sh servico2

# Configure environment
cp servico2/.env.template servico2/.env
nano servico2/.env  # Add your Gemini API key
```

### 2. Test Locally

```bash
# Run the service locally
./scripts/run-service-local.sh servico2 8001

# Test the service
curl http://localhost:8001/health
curl http://localhost:8001/info
```

### 3. Deploy to Production

```bash
# Add Bitbucket environment variable: SERVICO2_GEMINIKEY
# Push changes to trigger automatic deployment
git add .
git commit -m "Add servico2"
git push origin main
```

## 📁 Project Structure

```
franqai_agents/
├── servico1/                    # Template service
│   ├── app.py                   # Main LANGSERVE application
│   ├── config.py                # Configuration management
│   ├── requirements.txt         # Python dependencies
│   ├── Dockerfile              # Container configuration
│   ├── .env.template           # Environment template
│   ├── .gitignore              # Git ignore rules
│   └── .dockerignore           # Docker ignore rules
├── scripts/                     # Utility scripts
│   ├── create-new-service.sh    # Create new service from template
│   ├── build-service.sh         # Build Docker images
│   ├── run-service-local.sh     # Local development
│   └── deploy-to-gke.sh         # Manual GKE deployment
├── k8s/                         # Kubernetes templates
│   ├── deployment-template.yaml # Service deployment template
│   ├── ingress.yaml            # Ingress configuration
│   └── namespace.yaml          # Namespace setup
├── bitbucket-pipelines.yml      # CI/CD configuration
└── README.md                   # This file
```

## 🔧 Configuration

### Environment Variables

Each service requires specific environment variables:

#### Bitbucket Variables (Required)
- `{SERVICE_NAME}_GEMINIKEY`: Gemini API key for each service
- `GCP_PROJECT_ID`: Google Cloud Project ID
- `GKE_CLUSTER_NAME`: GKE cluster name
- `GKE_CLUSTER_REGION`: GKE cluster region
- `ARTIFACT_REGISTRY_REGION`: Artifact Registry region
- `ARTIFACT_REGISTRY_REPO`: Artifact Registry repository name
- `K8S_NAMESPACE`: Kubernetes namespace (franq-ai-agents)
- `GCLOUD_SERVICE_KEY`: Base64-encoded service account key

#### Service-Level Variables
- `SERVICE_NAME`: Service identifier (e.g., servico1, servico2)
- `GEMINI_API_KEY`: Gemini AI Studio API key
- `HOST`: Server host (default: 0.0.0.0)
- `PORT`: Server port (default: 8000)
- `LOG_LEVEL`: Logging level (default: INFO)

### API Key Naming Convention

API keys follow the pattern: `{SERVICE_NAME}_GEMINIKEY` (uppercase)

Examples:
- `SERVICO1_GEMINIKEY`
- `SERVICO2_GEMINIKEY`
- `CHATBOT_GEMINIKEY`

## 🐳 Docker

### Building Images

```bash
# Build specific service
./scripts/build-service.sh servico1

# Build with custom tag
./scripts/build-service.sh servico1 v1.0.0
```

### Running Containers

```bash
# Run with environment file
docker run -p 8000:8000 --env-file servico1/.env franq-ai-agents/servico1:latest

# Run with environment variables
docker run -p 8000:8000 \
  -e GEMINI_API_KEY=your_api_key \
  -e SERVICE_NAME=servico1 \
  franq-ai-agents/servico1:latest
```

## ☸️ Kubernetes Deployment

### Automatic Deployment (Recommended)

1. **Configure Bitbucket Variables**: Set all required environment variables
2. **Push Changes**: Automatic deployment triggers on push to main/develop
3. **Monitor**: Check Bitbucket Pipelines for deployment status

### Manual Deployment

```bash
# Set required environment variables
export GCP_PROJECT_ID="your-project"
export GKE_CLUSTER_NAME="your-cluster"
export GKE_CLUSTER_REGION="us-central1"
export ARTIFACT_REGISTRY_REGION="us-central1"
export ARTIFACT_REGISTRY_REPO="franq-ai-agents"
export K8S_NAMESPACE="franq-ai-agents"
export SERVICO1_GEMINIKEY="your-api-key"

# Deploy service
./scripts/deploy-to-gke.sh servico1
```

### Accessing Services

Services are accessible via path-based routing:

- **Base Domain**: `srvagents.franqtec.com.br`
- **Service URLs**:
  - `https://srvagents.franqtec.com.br/servico1`
  - `https://srvagents.franqtec.com.br/servico2`
- **Health Checks**: `https://srvagents.franqtec.com.br/{service}/health`
- **API Documentation**: `https://srvagents.franqtec.com.br/{service}/docs`

## 🔄 CI/CD Pipeline

### Automatic Triggers

- **Push to main**: Deploys all changed services
- **Push to develop**: Deploys all changed services
- **Manual trigger**: Deploy specific service

### Pipeline Features

- **Smart Detection**: Only builds/deploys changed services
- **Security**: API keys injected as Kubernetes secrets
- **Rollback**: Kubernetes rollout status monitoring
- **Multi-service**: Supports multiple services in single repository

### Custom Deployment

Use Bitbucket's custom pipeline feature:

1. Go to Bitbucket Pipelines
2. Select "Run custom pipeline"
3. Choose "deploy-service"
4. Set `SERVICE_NAME` variable (e.g., servico2)
5. Run pipeline

## 🛠️ Development

### Local Development Setup

```bash
# Clone repository
git clone <repository-url>
cd franqai_agents

# Create and configure service
./scripts/create-new-service.sh myservice
cp myservice/.env.template myservice/.env
# Edit myservice/.env with your API key

# Run locally
./scripts/run-service-local.sh myservice 8000
```

### Testing

```bash
# Health check
curl http://localhost:8000/health

# Service info
curl http://localhost:8000/info

# Chat endpoint
curl -X POST http://localhost:8000/myservice/chat \
  -H "Content-Type: application/json" \
  -d '{"input": "Hello, how are you?"}'

# LANGSERVE endpoints
curl -X POST http://localhost:8000/myservice/invoke \
  -H "Content-Type: application/json" \
  -d '{"input": {"input": "Hello"}}'
```

### Adding Dependencies

```bash
# Add to requirements.txt
echo "new-package==1.0.0" >> servico1/requirements.txt

# Rebuild image
./scripts/build-service.sh servico1
```

## 🔒 Security Best Practices

### ✅ Implemented Security Measures

- **No Hardcoded Secrets**: All API keys via environment variables
- **Kubernetes Secrets**: Secure secret injection in containers
- **Non-root Containers**: Services run as non-root user (UID 1000)
- **Resource Limits**: CPU and memory limits configured
- **Health Checks**: Liveness and readiness probes
- **Network Policies**: Namespace-level network isolation
- **Image Security**: Multi-stage builds, minimal base images

### 🚫 Security Guidelines

- **Never commit** `.env` files
- **Never hardcode** API keys in source code
- **Always use** Bitbucket variables for sensitive data
- **Regularly rotate** API keys
- **Monitor** service logs for security events

## 📊 Monitoring and Troubleshooting

### Health Checks

```bash
# Check service health
kubectl get pods -n franq-ai-agents
kubectl logs -f deployment/servico1 -n franq-ai-agents

# Check ingress
kubectl get ingress -n franq-ai-agents
kubectl describe ingress franq-ai-agents-ingress -n franq-ai-agents
```

### Common Issues

1. **Service Not Starting**: Check API key configuration
2. **404 Errors**: Verify ingress path configuration
3. **Build Failures**: Check Bitbucket variables
4. **Deployment Timeouts**: Check resource limits and health checks

## 🤝 Contributing

1. **Create Feature Branch**: `git checkout -b feature/new-service`
2. **Follow Naming Convention**: Use lowercase service names
3. **Test Locally**: Always test before pushing
4. **Update Documentation**: Update README for new features
5. **Security Review**: Ensure no secrets in commits

## 📝 License

This project is proprietary to Franq. All rights reserved.

## 📞 Support

For support and questions:
- **Internal Documentation**: Check confluence/wiki
- **Technical Issues**: Create Bitbucket issue
- **Security Concerns**: Contact security team immediately
