image: google/cloud-sdk:alpine

definitions:
  services:
    docker:
      memory: 2048

  caches:
    docker-cache: ~/.docker

  steps:
    - step: &build-and-deploy
        name: Build and Deploy Service
        services:
          - docker
        caches:
          - docker-cache
        script:
          # Install required tools
          - apk add --no-cache jq curl gettext
          # Authenticate with Google Cloud
          - echo $GCLOUD_SERVICE_KEY | base64 -d > gcloud-service-key.json
          - gcloud auth activate-service-account --key-file gcloud-service-key.json
          - gcloud config set project $GCP_PROJECT_ID
          # Configure Docker for Artifact Registry
          - gcloud auth configure-docker $ARTIFACT_REGISTRY_REGION-docker.pkg.dev
          # Detect which service to build based on changed files or manual trigger
          - |
            if [ -n "$SERVICE_NAME" ]; then
              echo "Manual deployment for service: $SERVICE_NAME"
              SERVICES_TO_BUILD="$SERVICE_NAME"
            else
              echo "Auto-detecting services to build based on changed files..."
              SERVICES_TO_BUILD=""
              for service_dir in servico*; do
                if [ -d "$service_dir" ]; then
                  if git diff --name-only HEAD~1 HEAD | grep -q "^$service_dir/"; then
                    SERVICES_TO_BUILD="$SERVICES_TO_BUILD $service_dir"
                  fi
                fi
              done
              # If no services detected, build all services (for initial deployment)
              if [ -z "$SERVICES_TO_BUILD" ]; then
                echo "No specific services detected, building all services..."
                SERVICES_TO_BUILD=$(ls -d servico*/ 2>/dev/null | sed 's|/||g' || echo "")
              fi
            fi
          - echo "Services to build: $SERVICES_TO_BUILD"
          # Build and deploy each service
          - |
            for service in $SERVICES_TO_BUILD; do
              if [ -d "$service" ]; then
                echo "Building and deploying service: $service"
                # Build Docker image
                cd $service
                IMAGE_TAG="$ARTIFACT_REGISTRY_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$ARTIFACT_REGISTRY_REPO/$service:$BITBUCKET_BUILD_NUMBER"
                IMAGE_LATEST="$ARTIFACT_REGISTRY_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$ARTIFACT_REGISTRY_REPO/$service:latest"
                docker build -t $IMAGE_TAG -t $IMAGE_LATEST .
                # Push to Artifact Registry
                docker push $IMAGE_TAG
                docker push $IMAGE_LATEST
                cd ..
                # Deploy to GKE
                echo "Deploying $service to GKE..."
                # Get GKE credentials
                gcloud container clusters get-credentials $GKE_CLUSTER_NAME --region $GKE_CLUSTER_REGION
                # Get the API key variable name for this service
                API_KEY_VAR_NAME=$(echo "${service}_GEMINIKEY" | tr '[:lower:]' '[:upper:]')
                # Get the API key value from environment variables
                API_KEY_VALUE=$(eval echo \$$API_KEY_VAR_NAME)
                if [ -z "$API_KEY_VALUE" ]; then
                  echo "ERROR: API key variable $API_KEY_VAR_NAME is not set!"
                  exit 1
                fi
                # Create or update Kubernetes secret
                kubectl create secret generic ${service}-secrets --from-literal=GEMINI_API_KEY="$API_KEY_VALUE" --namespace=$K8S_NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
                # Generate deployment YAML from template
                envsubst < k8s/deployment-template.yaml > k8s/deployment-${service}.yaml
                # Apply deployment
                kubectl apply -f k8s/deployment-${service}.yaml
                # Wait for deployment to be ready
                kubectl rollout status deployment/${service} --namespace=$K8S_NAMESPACE --timeout=300s
                echo "Successfully deployed $service"
              else
                echo "Service directory $service not found, skipping..."
              fi
            done

pipelines:
  default:
    - step: *build-and-deploy
  
  branches:
    main:
      - step: *build-and-deploy
    
    develop:
      - step: *build-and-deploy
  
  custom:
    deploy-service:
      - variables:
          - name: SERVICE_NAME
            description: "Name of the service to deploy (e.g., servico1, servico2)"
            required: true
      - step: *build-and-deploy
