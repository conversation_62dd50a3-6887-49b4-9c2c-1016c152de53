apiVersion: v1
kind: Namespace
metadata:
  name: franq-ai-agents
  labels:
    name: franq-ai-agents
    component: ai-agent
    project: franq-ai-agents
    environment: production
---
# Network policy for the namespace (optional security enhancement)
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: franq-ai-agents-network-policy
  namespace: franq-ai-agents
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    - namespaceSelector:
        matchLabels:
          name: gke-system
    - podSelector: {}
  egress:
  - {} # Allow all egress traffic (needed for external API calls to Gemini)
