apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${service}
  namespace: ${K8S_NAMESPACE}
  labels:
    app: ${service}
    version: "${BITBUCKET_BUILD_NUMBER}"
    component: ai-agent
    project: franq-ai-agents
spec:
  replicas: 2
  selector:
    matchLabels:
      app: ${service}
  template:
    metadata:
      labels:
        app: ${service}
        version: "${BITBUCKET_BUILD_NUMBER}"
        component: ai-agent
        project: franq-ai-agents
    spec:
      containers:
      - name: ${service}
        image: ${ARTIFACT_REGISTRY_REGION}-docker.pkg.dev/${GCP_PROJECT_ID}/${ARTIFACT_REGISTRY_REPO}/${service}:${BITBUCKET_BUILD_NUMBER}
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: SERVICE_NAME
          value: "${service}"
        - name: HOST
          value: "0.0.0.0"
        - name: PORT
          value: "8000"
        - name: GEMINI_API_KEY
          valueFrom:
            secretKeyRef:
              name: ${service}-secrets
              key: GEMINI_API_KEY
        - name: LOG_LEVEL
          value: "INFO"
        - name: CORS_ORIGINS
          value: "*"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      securityContext:
        fsGroup: 1000
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: ${service}-service
  namespace: ${K8S_NAMESPACE}
  labels:
    app: ${service}
    component: ai-agent
    project: franq-ai-agents
spec:
  selector:
    app: ${service}
  ports:
  - name: http
    port: 80
    targetPort: 8000
    protocol: TCP
  type: ClusterIP
