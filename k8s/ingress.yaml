apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: franq-ai-agents-ingress
  namespace: franq-ai-agents
  labels:
    component: ai-agent
    project: franq-ai-agents
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "franq-ai-agents-ip"
    networking.gke.io/managed-certificates: "franq-ai-agents-ssl-cert"
    kubernetes.io/ingress.allow-http: "false"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  rules:
  - host: srvagents.franqtec.com.br
    http:
      paths:
      # Service 1 routing
      - path: /servico1(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: servico1-service
            port:
              number: 80
      
      # Service 2 routing (add more services as needed)
      - path: /servico2(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: servico2-service
            port:
              number: 80
      
      # Default health check endpoint
      - path: /health
        pathType: Exact
        backend:
          service:
            name: servico1-service
            port:
              number: 80
      
      # Root path redirect to service info
      - path: /
        pathType: Exact
        backend:
          service:
            name: servico1-service
            port:
              number: 80

---
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: franq-ai-agents-ssl-cert
  namespace: franq-ai-agents
spec:
  domains:
    - srvagents.franqtec.com.br

---
# Global IP address for the ingress
apiVersion: compute.cnrm.cloud.google.com/v1beta1
kind: ComputeAddress
metadata:
  name: franq-ai-agents-ip
  namespace: franq-ai-agents
  labels:
    component: ai-agent
    project: franq-ai-agents
spec:
  location: global
  addressType: EXTERNAL
