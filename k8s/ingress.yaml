# ingress.yaml - Template for FranqAI Agents multi-service ingress
# This template supports multiple AI agent services with path-based routing
# Services are accessible at: srvagents.franqtec.com.br/{service_name}

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: franq-ai-agents-ingress
  namespace: franq-ai-agents
  labels:
    component: ai-agent
    project: franq-ai-agents
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/whitelist-source-range: "*************/32,************/32,**************/32"
    # Aumentando timeout do nginx para suportar operações de IA de longa duração
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"    # Timeout de leitura: 1 hora
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"    # Timeout de envio: 1 hora
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "3600" # Timeout de conexão: 1 hora
    # Configurações específicas para roteamento de múltiplos serviços
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/use-regex: "true"
    # Configurações de SSL e segurança
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  ingressClassName: nginx
  rules:
  - host: srvagents.franqtec.com.br
    http:
      paths:
      # Servico1 routing - Template service
      - path: /servico1(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: servico1-service
            port:
              number: 80

      # Servico2 routing - Add more services as needed
      - path: /servico2(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: servico2-service
            port:
              number: 80

      # Servico3 routing - Example for additional services
      - path: /servico3(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: servico3-service
            port:
              number: 80

      # Chatbot service routing - Example specialized service
      - path: /chatbot(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: chatbot-service
            port:
              number: 80

      # Global health check endpoint (routes to servico1 by default)
      - path: /health
        pathType: Exact
        backend:
          service:
            name: servico1-service
            port:
              number: 80

      # Root path - Service discovery endpoint
      - path: /
        pathType: Exact
        backend:
          service:
            name: servico1-service
            port:
              number: 80
