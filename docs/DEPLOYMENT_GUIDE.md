# Deployment Guide for FranqAI Agents

This guide provides step-by-step instructions for deploying services in the FranqAI Agents platform.

## 🚀 Deployment Overview

The platform supports multiple deployment methods:

1. **Automatic Deployment** (Recommended): Triggered by git push
2. **Manual Deployment**: Using custom Bitbucket pipeline
3. **Local Deployment**: For development and testing

## 📋 Prerequisites

### Required Accounts and Access
- [ ] Bitbucket repository access with admin permissions
- [ ] Google Cloud Platform project with billing enabled
- [ ] GKE cluster running and accessible
- [ ] Gemini AI Studio API keys
- [ ] Artifact Registry repository created

### Required Tools (for manual deployment)
- [ ] `gcloud` CLI installed and configured
- [ ] `kubectl` installed and configured
- [ ] `docker` installed and running
- [ ] `git` for version control

## 🔧 Initial Setup

### 1. Configure Bitbucket Variables

Navigate to **Repository Settings > Pipelines > Repository variables** and add:

#### Infrastructure Variables
```
GCP_PROJECT_ID = your-gcp-project-id
GKE_CLUSTER_NAME = your-gke-cluster-name
GKE_CLUSTER_REGION = us-central1-a
ARTIFACT_REGISTRY_REGION = us-central1
ARTIFACT_REGISTRY_REPO = franq-ai-agents
K8S_NAMESPACE = franq-ai-agents
```

#### Service Account (Secured)
```
GCLOUD_SERVICE_KEY = <base64-encoded-service-account-json>
```

#### API Keys (Secured)
```
SERVICO1_GEMINIKEY = your-gemini-api-key-for-service1
SERVICO2_GEMINIKEY = your-gemini-api-key-for-service2
```

### 2. Prepare GKE Cluster

```bash
# Create namespace
kubectl apply -f k8s/namespace.yaml

# Verify namespace
kubectl get namespace franq-ai-agents
```

### 3. Configure DNS (One-time setup)

```bash
# Get ingress IP
kubectl get ingress franq-ai-agents-ingress -n franq-ai-agents

# Configure DNS A record
# srvagents.franqtec.com.br -> <INGRESS_IP>
```

## 🔄 Automatic Deployment

### Triggering Automatic Deployment

Automatic deployment is triggered by:

1. **Push to main branch**: Deploys all changed services
2. **Push to develop branch**: Deploys all changed services

```bash
# Make changes to a service
echo "# Updated" >> servico1/README.md

# Commit and push
git add .
git commit -m "Update servico1"
git push origin main

# Monitor deployment in Bitbucket Pipelines
```

### Deployment Process

The pipeline automatically:

1. **Detects Changes**: Identifies modified services
2. **Builds Images**: Creates Docker images for changed services
3. **Pushes to Registry**: Uploads images to Artifact Registry
4. **Updates Secrets**: Creates/updates Kubernetes secrets
5. **Deploys Services**: Applies Kubernetes deployments
6. **Verifies Health**: Waits for services to be ready

### Monitoring Deployment

```bash
# Watch deployment progress
kubectl get deployments -n franq-ai-agents -w

# Check pod status
kubectl get pods -n franq-ai-agents

# View logs
kubectl logs -f deployment/servico1 -n franq-ai-agents
```

## 🎯 Manual Deployment

### Using Custom Pipeline

1. **Navigate to Bitbucket Pipelines**
2. **Click "Run pipeline"**
3. **Select "Custom: deploy-service"**
4. **Set variables**:
   ```
   SERVICE_NAME = servico1
   ```
5. **Run pipeline**

### Local Manual Deployment

```bash
# Set environment variables
export GCP_PROJECT_ID="your-project"
export GKE_CLUSTER_NAME="your-cluster"
export GKE_CLUSTER_REGION="us-central1-a"
export ARTIFACT_REGISTRY_REGION="us-central1"
export ARTIFACT_REGISTRY_REPO="franq-ai-agents"
export K8S_NAMESPACE="franq-ai-agents"
export SERVICO1_GEMINIKEY="your-api-key"

# Authenticate with GCP
gcloud auth login
gcloud config set project $GCP_PROJECT_ID

# Deploy service
./scripts/deploy-to-gke.sh servico1
```

## 🆕 Deploying New Services

### Step-by-Step Process

#### 1. Create New Service
```bash
# Create service from template
./scripts/create-new-service.sh servico2

# Configure environment
cp servico2/.env.template servico2/.env
nano servico2/.env  # Add your API key
```

#### 2. Test Locally
```bash
# Run service locally
./scripts/run-service-local.sh servico2 8001

# Test endpoints
curl http://localhost:8001/health
curl http://localhost:8001/info
```

#### 3. Configure Bitbucket Variable
```
Variable Name: SERVICO2_GEMINIKEY
Variable Value: your-gemini-api-key
Secured: ✓ (checked)
```

#### 4. Update Ingress (if needed)
```yaml
# Add to k8s/ingress.yaml
- path: /servico2(/|$)(.*)
  pathType: Prefix
  backend:
    service:
      name: servico2-service
      port:
        number: 80
```

#### 5. Deploy
```bash
# Commit and push
git add .
git commit -m "Add servico2"
git push origin main

# Or use manual deployment
# Bitbucket Pipelines > Custom: deploy-service > SERVICE_NAME=servico2
```

## 🔍 Verification and Testing

### Health Checks

```bash
# Check service health
curl https://srvagents.franqtec.com.br/servico1/health

# Expected response
{
  "status": "healthy",
  "service": "servico1",
  "version": "1.0.0"
}
```

### API Testing

```bash
# Test chat endpoint
curl -X POST https://srvagents.franqtec.com.br/servico1/chat \
  -H "Content-Type: application/json" \
  -d '{"input": "Hello, how are you?"}'

# Test LANGSERVE endpoint
curl -X POST https://srvagents.franqtec.com.br/servico1/invoke \
  -H "Content-Type: application/json" \
  -d '{"input": {"input": "Hello"}}'
```

### Kubernetes Verification

```bash
# Check deployments
kubectl get deployments -n franq-ai-agents

# Check services
kubectl get services -n franq-ai-agents

# Check ingress
kubectl get ingress -n franq-ai-agents

# Check secrets
kubectl get secrets -n franq-ai-agents
```

## 🔄 Updates and Rollbacks

### Updating Services

```bash
# Make changes to service code
nano servico1/app.py

# Commit and push (triggers automatic deployment)
git add .
git commit -m "Update servico1 logic"
git push origin main
```

### Rolling Back Deployments

```bash
# View rollout history
kubectl rollout history deployment/servico1 -n franq-ai-agents

# Rollback to previous version
kubectl rollout undo deployment/servico1 -n franq-ai-agents

# Rollback to specific revision
kubectl rollout undo deployment/servico1 --to-revision=2 -n franq-ai-agents
```

### Zero-Downtime Updates

The platform supports zero-downtime updates through:

1. **Rolling Updates**: Kubernetes gradually replaces old pods
2. **Health Checks**: Only healthy pods receive traffic
3. **Multiple Replicas**: At least one pod always available

## 🚨 Troubleshooting

### Common Deployment Issues

#### 1. Build Failures

**Symptom**: Pipeline fails during Docker build
```
Error: failed to solve: process "/bin/sh -c pip install -r requirements.txt" did not complete successfully
```

**Solution**:
```bash
# Check requirements.txt syntax
cat servico1/requirements.txt

# Test build locally
./scripts/build-service.sh servico1
```

#### 2. Authentication Errors

**Symptom**: Cannot push to Artifact Registry
```
Error: denied: Permission "artifactregistry.repositories.uploadArtifacts" denied
```

**Solution**:
- Verify `GCLOUD_SERVICE_KEY` is correctly base64 encoded
- Check service account has `roles/artifactregistry.writer`

#### 3. Deployment Timeouts

**Symptom**: Deployment doesn't become ready
```
Error: deployment "servico1" exceeded its progress deadline
```

**Solution**:
```bash
# Check pod logs
kubectl logs -f deployment/servico1 -n franq-ai-agents

# Check pod events
kubectl describe pod <pod-name> -n franq-ai-agents

# Common issues:
# - Missing API key
# - Health check failing
# - Resource limits too low
```

#### 4. Service Unreachable

**Symptom**: 404 errors when accessing service
```
curl: (22) The requested URL returned error: 404 Not Found
```

**Solution**:
```bash
# Check ingress configuration
kubectl describe ingress franq-ai-agents-ingress -n franq-ai-agents

# Check service endpoints
kubectl get endpoints -n franq-ai-agents

# Verify DNS resolution
nslookup srvagents.franqtec.com.br
```

### Debug Commands

```bash
# Check all resources
kubectl get all -n franq-ai-agents

# Check events
kubectl get events -n franq-ai-agents --sort-by='.lastTimestamp'

# Check logs
kubectl logs -f deployment/servico1 -n franq-ai-agents

# Check configuration
kubectl describe deployment servico1 -n franq-ai-agents
kubectl describe service servico1-service -n franq-ai-agents
```

## 📊 Monitoring Deployments

### Deployment Metrics

```bash
# Check deployment status
kubectl get deployments -n franq-ai-agents

# Check replica status
kubectl get replicasets -n franq-ai-agents

# Check pod status
kubectl get pods -n franq-ai-agents -o wide
```

### Performance Monitoring

```bash
# Check resource usage
kubectl top pods -n franq-ai-agents

# Check node resources
kubectl top nodes

# Check service endpoints
kubectl get endpoints -n franq-ai-agents
```

## 📋 Deployment Checklist

### Pre-Deployment
- [ ] Service tested locally
- [ ] Docker image builds successfully
- [ ] API key configured in Bitbucket
- [ ] All required environment variables set
- [ ] Code reviewed and approved

### During Deployment
- [ ] Monitor Bitbucket Pipeline progress
- [ ] Check Kubernetes deployment status
- [ ] Verify pod health and readiness
- [ ] Test service endpoints

### Post-Deployment
- [ ] Verify service accessibility via ingress
- [ ] Test all API endpoints
- [ ] Check logs for errors
- [ ] Monitor resource usage
- [ ] Update documentation if needed

## 🔗 Quick Reference

### Useful URLs
- **Bitbucket Pipelines**: `https://bitbucket.org/your-workspace/franqai_agents/addon/pipelines/home`
- **GCP Console**: `https://console.cloud.google.com/kubernetes/workload?project=your-project`
- **Service Base URL**: `https://srvagents.franqtec.com.br`

### Key Commands
```bash
# Quick deployment
git add . && git commit -m "Deploy changes" && git push origin main

# Quick health check
curl https://srvagents.franqtec.com.br/servico1/health

# Quick logs
kubectl logs -f deployment/servico1 -n franq-ai-agents

# Quick status
kubectl get pods -n franq-ai-agents
```
