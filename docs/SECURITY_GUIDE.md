# Security Guide for FranqAI Agents

This document outlines security best practices, implementation details, and guidelines for the FranqAI Agents platform.

## 🔒 Security Architecture

### Defense in Depth

The platform implements multiple layers of security:

1. **Application Layer**: Secure coding practices, input validation
2. **Container Layer**: Non-root users, minimal images, resource limits
3. **Kubernetes Layer**: Network policies, RBAC, secrets management
4. **Infrastructure Layer**: GKE security features, VPC isolation
5. **CI/CD Layer**: Secure variable management, image scanning

### Security Principles

- **Least Privilege**: Minimal permissions for all components
- **Zero Trust**: No implicit trust between components
- **Secrets Isolation**: No secrets in code or images
- **Audit Trail**: All actions logged and traceable
- **Regular Updates**: Dependencies and base images kept current

## 🔐 Secrets Management

### ✅ Secure Practices

#### Bitbucket Variables
```yaml
# Repository Settings > Pipelines > Repository variables
Variables:
  - Name: SERVICO1_GEMINIKEY
    Value: your_api_key_here
    Secured: ✓ (checked)
  
  - Name: GCLOUD_SERVICE_KEY
    Value: base64_encoded_service_account_json
    Secured: ✓ (checked)
```

#### Kubernetes Secrets
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: servico1-secrets
  namespace: franq-ai-agents
type: Opaque
data:
  GEMINI_API_KEY: <base64-encoded-value>
```

#### Environment Variable Injection
```yaml
env:
- name: GEMINI_API_KEY
  valueFrom:
    secretKeyRef:
      name: servico1-secrets
      key: GEMINI_API_KEY
```

### ❌ Insecure Practices to Avoid

```python
# ❌ NEVER do this - hardcoded secrets
GEMINI_API_KEY = "AIzaSyC-your-actual-api-key-here"

# ❌ NEVER do this - secrets in environment files committed to git
# .env file in git repository
GEMINI_API_KEY=AIzaSyC-your-actual-api-key-here

# ❌ NEVER do this - secrets in Dockerfile
ENV GEMINI_API_KEY=AIzaSyC-your-actual-api-key-here

# ❌ NEVER do this - secrets in deployment YAML in git
env:
- name: GEMINI_API_KEY
  value: "AIzaSyC-your-actual-api-key-here"
```

## 🐳 Container Security

### Secure Dockerfile Practices

```dockerfile
# ✅ Use specific, minimal base images
FROM python:3.11-slim as production

# ✅ Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# ✅ Set working directory
WORKDIR /app

# ✅ Copy files with proper ownership
COPY --chown=appuser:appuser . .

# ✅ Switch to non-root user
USER appuser

# ✅ Use HEALTHCHECK
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1
```

### Container Runtime Security

```yaml
# Kubernetes security context
securityContext:
  runAsNonRoot: true
  runAsUser: 1000
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: false  # Set to true if possible
  capabilities:
    drop:
    - ALL
```

### Resource Limits

```yaml
resources:
  requests:
    memory: "256Mi"
    cpu: "250m"
  limits:
    memory: "512Mi"
    cpu: "500m"
```

## ☸️ Kubernetes Security

### Network Policies

```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: franq-ai-agents-network-policy
  namespace: franq-ai-agents
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    - podSelector: {}
  egress:
  - {} # Allow all egress (needed for external API calls)
```

### RBAC Configuration

```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: franq-ai-agents
  name: franq-ai-agents-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch", "update", "patch"]
```

### Pod Security Standards

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: franq-ai-agents
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
```

## 🌐 Network Security

### TLS/SSL Configuration

```yaml
# Managed certificate for HTTPS
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: franq-ai-agents-ssl-cert
spec:
  domains:
    - srvagents.franqtec.com.br
```

### Ingress Security

```yaml
metadata:
  annotations:
    kubernetes.io/ingress.allow-http: "false"  # HTTPS only
    kubernetes.io/ingress.global-static-ip-name: "franq-ai-agents-ip"
    networking.gke.io/managed-certificates: "franq-ai-agents-ssl-cert"
```

### CORS Configuration

```python
# Secure CORS configuration
CORS_ORIGINS = os.getenv("CORS_ORIGINS", "https://franqtec.com.br").split(",")

app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ORIGINS,
    allow_credentials=False,  # Don't allow credentials
    allow_methods=["GET", "POST"],  # Minimal methods
    allow_headers=["Content-Type", "Authorization"],
)
```

## 🔍 Security Monitoring

### Logging Security Events

```python
import logging
import structlog

# Configure structured logging
logger = structlog.get_logger()

# Log security events
def log_security_event(event_type, details):
    logger.warning(
        "security_event",
        event_type=event_type,
        details=details,
        timestamp=datetime.utcnow().isoformat(),
        service=SERVICE_NAME
    )

# Example usage
@app.middleware("http")
async def security_middleware(request: Request, call_next):
    # Log suspicious requests
    if len(request.url.path) > 1000:
        log_security_event("suspicious_request", {
            "path_length": len(request.url.path),
            "client_ip": request.client.host
        })
    
    response = await call_next(request)
    return response
```

### Health Check Security

```python
@app.get("/health")
async def health_check():
    """Secure health check - no sensitive information"""
    return {
        "status": "healthy",
        "service": SERVICE_NAME,
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat()
        # ❌ Don't include: API keys, internal IPs, detailed errors
    }
```

## 🚨 Incident Response

### Security Incident Checklist

1. **Immediate Response**
   - [ ] Identify affected services
   - [ ] Isolate compromised components
   - [ ] Rotate all API keys
   - [ ] Check audit logs

2. **Investigation**
   - [ ] Analyze logs for attack vectors
   - [ ] Check for data exfiltration
   - [ ] Identify root cause
   - [ ] Document timeline

3. **Recovery**
   - [ ] Deploy security patches
   - [ ] Update configurations
   - [ ] Verify system integrity
   - [ ] Resume normal operations

4. **Post-Incident**
   - [ ] Update security procedures
   - [ ] Conduct lessons learned
   - [ ] Improve monitoring
   - [ ] Train team members

### Emergency Procedures

```bash
# Emergency API key rotation
# 1. Generate new API keys in Gemini AI Studio
# 2. Update Bitbucket variables
export NEW_API_KEY="new_secure_api_key"
kubectl create secret generic servico1-secrets \
  --from-literal=GEMINI_API_KEY="$NEW_API_KEY" \
  --namespace=franq-ai-agents \
  --dry-run=client -o yaml | kubectl apply -f -

# 3. Restart deployments to pick up new secrets
kubectl rollout restart deployment/servico1 -n franq-ai-agents

# Emergency service isolation
kubectl scale deployment servico1 --replicas=0 -n franq-ai-agents
```

## 🔧 Security Tools and Automation

### Image Scanning

```yaml
# Add to bitbucket-pipelines.yml
- step:
    name: Security Scan
    script:
      - docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
          aquasec/trivy image $IMAGE_TAG
```

### Dependency Scanning

```bash
# Add to requirements.txt validation
pip-audit --requirement requirements.txt --format=json
```

### Secret Scanning

```bash
# Pre-commit hook for secret detection
#!/bin/bash
if git diff --cached --name-only | xargs grep -l "AIzaSy\|sk-\|pk_"; then
    echo "❌ Potential secrets detected in staged files!"
    exit 1
fi
```

## 📋 Security Checklist

### Development Phase
- [ ] No hardcoded secrets in code
- [ ] Input validation implemented
- [ ] Error handling doesn't leak information
- [ ] Dependencies are up to date
- [ ] Code reviewed for security issues

### Deployment Phase
- [ ] Secrets configured in Bitbucket variables
- [ ] Container runs as non-root user
- [ ] Resource limits configured
- [ ] Health checks implemented
- [ ] Network policies applied

### Production Phase
- [ ] HTTPS enforced
- [ ] Monitoring and alerting configured
- [ ] Backup and recovery procedures tested
- [ ] Incident response plan documented
- [ ] Regular security assessments scheduled

## 📞 Security Contacts

- **Security Team**: <EMAIL>
- **Emergency**: +55 (11) 9999-9999
- **Incident Reporting**: <EMAIL>

## 📚 Additional Resources

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [Kubernetes Security Best Practices](https://kubernetes.io/docs/concepts/security/)
- [Google Cloud Security](https://cloud.google.com/security)
- [Container Security Guide](https://cloud.google.com/container-optimized-os/docs/concepts/security)
