# Environment Variables Configuration Guide

This document provides comprehensive information about environment variables used in the FranqAI Agents platform.

## 🔧 Bitbucket Repository Variables

Configure these variables in your Bitbucket repository settings under **Repository Settings > Pipelines > Repository variables**.

### Required Variables

| Variable Name | Description | Example Value | Secured |
|---------------|-------------|---------------|---------|
| `GCP_PROJECT_ID` | Google Cloud Project ID | `franq-ai-production` | No |
| `GKE_CLUSTER_NAME` | GKE cluster name | `franq-ai-cluster` | No |
| `GKE_CLUSTER_REGION` | GKE cluster region | `us-central1-a` | No |
| `ARTIFACT_REGISTRY_REGION` | Artifact Registry region | `us-central1` | No |
| `ARTIFACT_REGISTRY_REPO` | Artifact Registry repository | `franq-ai-agents` | No |
| `K8S_NAMESPACE` | Kubernetes namespace | `franq-ai-agents` | No |
| `GCLOUD_SERVICE_KEY` | Base64-encoded service account JSON | `ewogICJ0eXBlIjogInNlcnZpY2VfYWNjb3VudCIsC...` | **Yes** |

### Service-Specific API Keys

Each service requires its own API key variable following the naming pattern: `{SERVICE_NAME}_GEMINIKEY`

| Service | Variable Name | Description | Secured |
|---------|---------------|-------------|---------|
| servico1 | `SERVICO1_GEMINIKEY` | Gemini API key for servico1 | **Yes** |
| servico2 | `SERVICO2_GEMINIKEY` | Gemini API key for servico2 | **Yes** |
| chatbot | `CHATBOT_GEMINIKEY` | Gemini API key for chatbot service | **Yes** |

### Optional Variables

| Variable Name | Description | Default Value | Secured |
|---------------|-------------|---------------|---------|
| `DOCKER_REGISTRY_MIRROR` | Docker registry mirror for faster builds | `mirror.gcr.io` | No |
| `BUILD_TIMEOUT` | Build timeout in seconds | `1800` | No |
| `DEPLOYMENT_TIMEOUT` | Deployment timeout in seconds | `300` | No |

## 🏠 Local Development Variables

For local development, create a `.env` file in each service directory:

### Service .env Template

```bash
# Service Configuration
SERVICE_NAME=servico1
SERVICE_VERSION=1.0.0

# Server Configuration
HOST=0.0.0.0
PORT=8000

# Gemini AI Configuration
GEMINI_API_KEY=your_actual_gemini_api_key_here
GEMINI_MODEL=gemini-pro
GEMINI_TEMPERATURE=0.7

# Logging Configuration
LOG_LEVEL=INFO

# Security Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=30
```

### Development Environment Setup

```bash
# Copy template
cp servico1/.env.template servico1/.env

# Edit with your values
nano servico1/.env

# Verify configuration
source servico1/.env
echo "Service: $SERVICE_NAME"
echo "API Key configured: $([ -n "$GEMINI_API_KEY" ] && echo "Yes" || echo "No")"
```

## ☸️ Kubernetes Environment Variables

These variables are automatically injected into containers during deployment:

### Container Environment Variables

| Variable | Source | Description |
|----------|--------|-------------|
| `SERVICE_NAME` | Deployment template | Service identifier |
| `HOST` | Deployment template | Server bind address |
| `PORT` | Deployment template | Server port |
| `GEMINI_API_KEY` | Kubernetes Secret | Gemini API key from Bitbucket variables |
| `LOG_LEVEL` | Deployment template | Logging level |
| `CORS_ORIGINS` | Deployment template | CORS allowed origins |

### Kubernetes Secrets

Secrets are automatically created by the CI/CD pipeline:

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: servico1-secrets
  namespace: franq-ai-agents
type: Opaque
data:
  GEMINI_API_KEY: <base64-encoded-api-key>
```

## 🔒 Security Configuration

### Service Account Configuration

Create a service account with minimal required permissions:

```json
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
```

### Required IAM Roles

The service account needs these roles:

- `roles/container.developer` - For GKE access
- `roles/artifactregistry.writer` - For pushing images
- `roles/secretmanager.secretAccessor` - For accessing secrets (if using Secret Manager)

### Environment Variable Validation

Services validate required environment variables on startup:

```python
# Example validation in config.py
required_vars = [
    "SERVICE_NAME",
    "GEMINI_API_KEY",
    "HOST",
    "PORT"
]

for var in required_vars:
    if not os.getenv(var):
        raise ValueError(f"Required environment variable {var} is not set")
```

## 🧪 Testing Configuration

### Local Testing

```bash
# Test environment loading
cd servico1
source .env
python -c "
import os
from config import get_config
config = get_config()
print(f'Service: {config.service_name}')
print(f'API Key configured: {bool(config.gemini_api_key)}')
"
```

### Container Testing

```bash
# Test container environment
docker run --env-file servico1/.env franq-ai-agents/servico1:latest python -c "
import os
print('Environment variables:')
for key in ['SERVICE_NAME', 'HOST', 'PORT', 'GEMINI_API_KEY']:
    value = os.getenv(key, 'NOT_SET')
    print(f'{key}: {value if key != \"GEMINI_API_KEY\" else \"***\" if value != \"NOT_SET\" else \"NOT_SET\"}')
"
```

### Kubernetes Testing

```bash
# Check environment in running pod
kubectl exec -it deployment/servico1 -n franq-ai-agents -- env | grep -E "(SERVICE_NAME|HOST|PORT|GEMINI_API_KEY)"

# Check secret mounting
kubectl describe secret servico1-secrets -n franq-ai-agents
```

## 🚨 Troubleshooting

### Common Issues

1. **Missing API Key**
   ```
   Error: Required environment variable GEMINI_API_KEY is not set
   ```
   - Check Bitbucket variable `{SERVICE_NAME}_GEMINIKEY`
   - Verify variable name follows uppercase convention

2. **Invalid Service Account**
   ```
   Error: Could not load default credentials
   ```
   - Check `GCLOUD_SERVICE_KEY` is properly base64 encoded
   - Verify service account has required permissions

3. **Deployment Timeout**
   ```
   Error: deployment "servico1" exceeded its progress deadline
   ```
   - Check resource limits in deployment template
   - Verify health check endpoints are responding

### Debug Commands

```bash
# Check Bitbucket variables
# (Run in Bitbucket Pipelines)
echo "Checking environment variables..."
echo "GCP_PROJECT_ID: $GCP_PROJECT_ID"
echo "Service API key set: $([ -n "$SERVICO1_GEMINIKEY" ] && echo "Yes" || echo "No")"

# Check Kubernetes secrets
kubectl get secrets -n franq-ai-agents
kubectl describe secret servico1-secrets -n franq-ai-agents

# Check pod environment
kubectl exec deployment/servico1 -n franq-ai-agents -- printenv | sort
```

## 📋 Checklist

Before deploying a new service, verify:

- [ ] Service directory created from template
- [ ] `.env` file configured for local development
- [ ] Bitbucket variable `{SERVICE_NAME}_GEMINIKEY` added
- [ ] Service tested locally
- [ ] Docker image builds successfully
- [ ] All required Bitbucket variables configured
- [ ] Service account has necessary permissions
- [ ] Ingress configuration updated (if needed)
