#!/bin/bash

# Script to create a new service from the template
# Usage: ./scripts/create-new-service.sh <new_service_name>

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if new service name is provided
if [ -z "$1" ]; then
    print_error "New service name is required"
    echo "Usage: $0 <new_service_name>"
    echo "Example: $0 servico2"
    exit 1
fi

NEW_SERVICE_NAME="$1"
TEMPLATE_SERVICE="servico1"

# Validate service name format
if [[ ! "$NEW_SERVICE_NAME" =~ ^[a-z][a-z0-9]*$ ]]; then
    print_error "Service name must start with a lowercase letter and contain only lowercase letters and numbers"
    exit 1
fi

# Check if template service exists
if [ ! -d "$TEMPLATE_SERVICE" ]; then
    print_error "Template service '$TEMPLATE_SERVICE' does not exist"
    exit 1
fi

# Check if new service already exists
if [ -d "$NEW_SERVICE_NAME" ]; then
    print_error "Service '$NEW_SERVICE_NAME' already exists"
    exit 1
fi

print_info "Creating new service '$NEW_SERVICE_NAME' from template '$TEMPLATE_SERVICE'"

# Copy template service
cp -r "$TEMPLATE_SERVICE" "$NEW_SERVICE_NAME"

# Update configuration files
print_info "Updating configuration files..."

# Update .env.template
sed -i "s/SERVICE_NAME=servico1/SERVICE_NAME=$NEW_SERVICE_NAME/g" "$NEW_SERVICE_NAME/.env.template"
sed -i "s/# This should be set via Bitbucket variables as SERVICO1_GEMINIKEY/# This should be set via Bitbucket variables as ${NEW_SERVICE_NAME^^}_GEMINIKEY/g" "$NEW_SERVICE_NAME/.env.template"

# Update config.py default service name
sed -i "s/default=\"servico1\"/default=\"$NEW_SERVICE_NAME\"/g" "$NEW_SERVICE_NAME/config.py"

# Update app.py default service name
sed -i "s/SERVICE_NAME = os.getenv(\"SERVICE_NAME\", \"servico1\")/SERVICE_NAME = os.getenv(\"SERVICE_NAME\", \"$NEW_SERVICE_NAME\")/g" "$NEW_SERVICE_NAME/app.py"

print_info "Service '$NEW_SERVICE_NAME' created successfully!"
print_info ""
print_info "Next steps:"
print_info "1. Copy the environment template:"
print_info "   cp $NEW_SERVICE_NAME/.env.template $NEW_SERVICE_NAME/.env"
print_info ""
print_info "2. Edit the .env file with your API keys:"
print_info "   nano $NEW_SERVICE_NAME/.env"
print_info ""
print_info "3. Add the Bitbucket environment variable:"
print_info "   Variable name: ${NEW_SERVICE_NAME^^}_GEMINIKEY"
print_info "   Variable value: Your Gemini API key"
print_info ""
print_info "4. Test locally:"
print_info "   ./scripts/run-service-local.sh $NEW_SERVICE_NAME"
print_info ""
print_info "5. Build Docker image:"
print_info "   ./scripts/build-service.sh $NEW_SERVICE_NAME"
print_info ""
print_info "6. Deploy via Bitbucket Pipelines:"
print_info "   - Push your changes to trigger automatic deployment"
print_info "   - Or use custom pipeline: SERVICE_NAME=$NEW_SERVICE_NAME"
