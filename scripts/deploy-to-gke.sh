#!/bin/bash

# Manual deployment script for GKE
# Usage: ./scripts/deploy-to-gke.sh <service_name>

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if service name is provided
if [ -z "$1" ]; then
    print_error "Service name is required"
    echo "Usage: $0 <service_name>"
    echo "Example: $0 servico1"
    exit 1
fi

SERVICE_NAME="$1"

# Check if service directory exists
if [ ! -d "$SERVICE_NAME" ]; then
    print_error "Service directory '$SERVICE_NAME' does not exist"
    exit 1
fi

# Check required environment variables
REQUIRED_VARS=(
    "GCP_PROJECT_ID"
    "GKE_CLUSTER_NAME"
    "GKE_CLUSTER_REGION"
    "ARTIFACT_REGISTRY_REGION"
    "ARTIFACT_REGISTRY_REPO"
    "K8S_NAMESPACE"
)

for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        print_error "Environment variable $var is required"
        exit 1
    fi
done

# Check for API key
API_KEY_VAR_NAME=$(echo "${SERVICE_NAME}_GEMINIKEY" | tr '[:lower:]' '[:upper:]')
API_KEY_VALUE="${!API_KEY_VAR_NAME}"

if [ -z "$API_KEY_VALUE" ]; then
    print_error "API key variable $API_KEY_VAR_NAME is not set"
    exit 1
fi

print_info "Deploying service: $SERVICE_NAME"
print_info "Project: $GCP_PROJECT_ID"
print_info "Cluster: $GKE_CLUSTER_NAME ($GKE_CLUSTER_REGION)"
print_info "Namespace: $K8S_NAMESPACE"

# Authenticate with GKE
print_info "Getting GKE credentials..."
gcloud container clusters get-credentials "$GKE_CLUSTER_NAME" --region "$GKE_CLUSTER_REGION" --project "$GCP_PROJECT_ID"

# Create namespace if it doesn't exist
print_info "Ensuring namespace exists..."
kubectl create namespace "$K8S_NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -

# Create or update secret
print_info "Creating/updating Kubernetes secret..."
kubectl create secret generic "${SERVICE_NAME}-secrets" \
    --from-literal=GEMINI_API_KEY="$API_KEY_VALUE" \
    --namespace="$K8S_NAMESPACE" \
    --dry-run=client -o yaml | kubectl apply -f -

# Generate deployment YAML
print_info "Generating deployment configuration..."
export service="$SERVICE_NAME"
export BITBUCKET_BUILD_NUMBER="${BITBUCKET_BUILD_NUMBER:-manual-$(date +%s)}"

envsubst < k8s/deployment-template.yaml > "k8s/deployment-${SERVICE_NAME}.yaml"

# Apply deployment
print_info "Applying deployment..."
kubectl apply -f "k8s/deployment-${SERVICE_NAME}.yaml"

# Wait for deployment
print_info "Waiting for deployment to be ready..."
kubectl rollout status deployment/"$SERVICE_NAME" --namespace="$K8S_NAMESPACE" --timeout=300s

# Show deployment status
print_info "Deployment status:"
kubectl get deployment "$SERVICE_NAME" --namespace="$K8S_NAMESPACE"
kubectl get pods -l app="$SERVICE_NAME" --namespace="$K8S_NAMESPACE"

print_info "Service deployed successfully!"
print_info "Service will be available at: https://srvagents.franqtec.com.br/$SERVICE_NAME"
