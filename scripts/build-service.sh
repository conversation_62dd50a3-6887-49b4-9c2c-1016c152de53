#!/bin/bash

# Build script for individual services
# Usage: ./scripts/build-service.sh <service_name> [tag]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if service name is provided
if [ -z "$1" ]; then
    print_error "Service name is required"
    echo "Usage: $0 <service_name> [tag]"
    echo "Example: $0 servico1 latest"
    exit 1
fi

SERVICE_NAME="$1"
TAG="${2:-latest}"

# Check if service directory exists
if [ ! -d "$SERVICE_NAME" ]; then
    print_error "Service directory '$SERVICE_NAME' does not exist"
    exit 1
fi

# Check if Dockerfile exists
if [ ! -f "$SERVICE_NAME/Dockerfile" ]; then
    print_error "Dockerfile not found in '$SERVICE_NAME' directory"
    exit 1
fi

print_info "Building Docker image for service: $SERVICE_NAME"
print_info "Tag: $TAG"

# Build the Docker image
cd "$SERVICE_NAME"

IMAGE_NAME="franq-ai-agents/$SERVICE_NAME:$TAG"

print_info "Building image: $IMAGE_NAME"
docker build -t "$IMAGE_NAME" .

if [ $? -eq 0 ]; then
    print_info "Successfully built image: $IMAGE_NAME"
    
    # Show image details
    print_info "Image details:"
    docker images "$IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    
    print_info "To run the service locally:"
    echo "docker run -p 8000:8000 --env-file .env $IMAGE_NAME"
else
    print_error "Failed to build image for $SERVICE_NAME"
    exit 1
fi
