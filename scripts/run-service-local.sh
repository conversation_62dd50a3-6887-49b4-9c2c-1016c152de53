#!/bin/bash

# Local development script for running services
# Usage: ./scripts/run-service-local.sh <service_name> [port]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if service name is provided
if [ -z "$1" ]; then
    print_error "Service name is required"
    echo "Usage: $0 <service_name> [port]"
    echo "Example: $0 servico1 8000"
    exit 1
fi

SERVICE_NAME="$1"
PORT="${2:-8000}"

# Check if service directory exists
if [ ! -d "$SERVICE_NAME" ]; then
    print_error "Service directory '$SERVICE_NAME' does not exist"
    exit 1
fi

# Check if .env file exists
if [ ! -f "$SERVICE_NAME/.env" ]; then
    print_warning ".env file not found in '$SERVICE_NAME' directory"
    if [ -f "$SERVICE_NAME/.env.template" ]; then
        print_info "Found .env.template file. Please copy it to .env and configure your API keys:"
        echo "cp $SERVICE_NAME/.env.template $SERVICE_NAME/.env"
        echo "Then edit $SERVICE_NAME/.env with your actual API keys"
        exit 1
    else
        print_error "No .env.template file found either. Please create environment configuration."
        exit 1
    fi
fi

print_info "Starting service: $SERVICE_NAME on port $PORT"

# Check if virtual environment exists
if [ ! -d "$SERVICE_NAME/venv" ]; then
    print_info "Creating virtual environment..."
    cd "$SERVICE_NAME"
    python3 -m venv venv
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
    cd ..
fi

# Run the service
cd "$SERVICE_NAME"
source venv/bin/activate

# Load environment variables
export $(cat .env | grep -v '^#' | xargs)
export PORT="$PORT"

print_info "Environment loaded. Starting service..."
print_info "Service will be available at: http://localhost:$PORT"
print_info "Health check: http://localhost:$PORT/health"
print_info "Service info: http://localhost:$PORT/info"
print_info "API docs: http://localhost:$PORT/docs"

# Start the service
python app.py
