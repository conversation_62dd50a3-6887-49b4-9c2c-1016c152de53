# Environment files
.env
.env.*

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode
.idea
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation
README.md
*.md

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*

# CI/CD
.github
.gitlab-ci.yml
bitbucket-pipelines.yml

# Kubernetes
k8s/
kubernetes/
*.yaml
*.yml

# Testing
tests/
test_*
*_test.py

# Temporary files
*.tmp
*.temp
.tmp/
