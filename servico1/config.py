"""
Configuration module for the AI Agent service
Handles environment variables and service configuration
"""

import os
from typing import Optional
from pydantic import BaseSettings, Field


class ServiceConfig(BaseSettings):
    """Service configuration using Pydantic BaseSettings for validation"""
    
    # Service identification
    service_name: str = Field(default="servico1", env="SERVICE_NAME")
    service_version: str = Field(default="1.0.0", env="SERVICE_VERSION")
    
    # Server configuration
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    
    # Gemini AI configuration
    gemini_api_key: str = Field(..., env="GEMINI_API_KEY")
    gemini_model: str = Field(default="gemini-pro", env="GEMINI_MODEL")
    gemini_temperature: float = Field(default=0.7, env="GEMINI_TEMPERATURE")
    
    # Logging configuration
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Security configuration
    cors_origins: str = Field(default="*", env="CORS_ORIGINS")
    
    # Health check configuration
    health_check_timeout: int = Field(default=30, env="HEALTH_CHECK_TIMEOUT")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


def get_config() -> ServiceConfig:
    """Get service configuration instance"""
    return ServiceConfig()


def validate_config(config: ServiceConfig) -> bool:
    """Validate configuration and return True if valid"""
    required_fields = ["gemini_api_key"]
    
    for field in required_fields:
        value = getattr(config, field)
        if not value:
            raise ValueError(f"Required configuration field '{field}' is missing or empty")
    
    return True


# Global configuration instance
config = get_config()
validate_config(config)
