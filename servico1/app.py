#!/usr/bin/env python3
"""
LANGSERVE-based AI Agent Service Template
This is a template service that can be copied and renamed to create new AI agents.
"""

import os
import logging
from typing import Dict, Any, List
from fastapi import FastAPI, HTTPException
from langserve import add_routes
from langchain_core.prompts import <PERSON>t<PERSON><PERSON><PERSON><PERSON><PERSON>plate
from langchain_core.output_parsers import StrO<PERSON>putPars<PERSON>
from langchain_google_genai import Cha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_core.runnables import <PERSON>nableLambda
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment variables
SERVICE_NAME = os.getenv("SERVICE_NAME", "servico1")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
PORT = int(os.getenv("PORT", "8000"))
HOST = os.getenv("HOST", "0.0.0.0")

# Validate required environment variables
if not GEMINI_API_KEY:
    raise ValueError(f"GEMINI_API_KEY environment variable is required for {SERVICE_NAME}")

# Initialize FastAPI app
app = FastAPI(
    title=f"FranqAI Agent - {SERVICE_NAME}",
    description=f"AI Agent service {SERVICE_NAME} powered by LANGSERVE and Gemini AI",
    version="1.0.0",
)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint for Kubernetes probes"""
    return {
        "status": "healthy",
        "service": SERVICE_NAME,
        "version": "1.0.0"
    }

# Service info endpoint
@app.get("/info")
async def service_info():
    """Service information endpoint"""
    return {
        "service_name": SERVICE_NAME,
        "description": f"AI Agent service {SERVICE_NAME}",
        "endpoints": [
            f"/{SERVICE_NAME}/invoke",
            f"/{SERVICE_NAME}/batch",
            f"/{SERVICE_NAME}/stream",
            "/health",
            "/info"
        ]
    }

# Initialize Gemini AI model
try:
    llm = ChatGoogleGenerativeAI(
        model="gemini-pro",
        google_api_key=GEMINI_API_KEY,
        temperature=0.7,
        convert_system_message_to_human=True
    )
    logger.info(f"Successfully initialized Gemini AI model for {SERVICE_NAME}")
except Exception as e:
    logger.error(f"Failed to initialize Gemini AI model: {e}")
    raise

# Define the prompt template
prompt_template = ChatPromptTemplate.from_messages([
    ("system", f"You are an AI assistant for the {SERVICE_NAME} service. "
               "You are helpful, accurate, and provide clear responses. "
               "Always identify yourself as part of the FranqAI agent system."),
    ("human", "{input}")
])

# Create the chain
def create_chain():
    """Create the LANGSERVE chain"""
    return prompt_template | llm | StrOutputParser()

# Input/Output models for API documentation
class AgentInput(BaseModel):
    input: str
    
class AgentOutput(BaseModel):
    output: str

# Create the chain instance
chain = create_chain()

# Add LANGSERVE routes
add_routes(
    app,
    chain,
    path=f"/{SERVICE_NAME}",
    input_type=AgentInput,
    output_type=AgentOutput,
)

# Custom endpoint for direct interaction
@app.post(f"/{SERVICE_NAME}/chat")
async def chat_endpoint(request: AgentInput) -> AgentOutput:
    """Direct chat endpoint for the AI agent"""
    try:
        response = await chain.ainvoke({"input": request.input})
        return AgentOutput(output=response)
    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    logger.info(f"Starting {SERVICE_NAME} service on {HOST}:{PORT}")
    uvicorn.run(app, host=HOST, port=PORT)
