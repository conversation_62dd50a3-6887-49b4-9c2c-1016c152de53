# Service Configuration Template for servico1
# Copy this file to .env and fill in the actual values
# NEVER commit .env files to version control

# Service identification
SERVICE_NAME=servico1
SERVICE_VERSION=1.0.0

# Server configuration
HOST=0.0.0.0
PORT=8000

# Gemini AI configuration
# This should be set via Bitbucket variables as SERVICO1_GEMINIKEY
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-pro
GEMINI_TEMPERATURE=0.7

# Logging configuration
LOG_LEVEL=INFO

# Security configuration
CORS_ORIGINS=*

# Health check configuration
HEALTH_CHECK_TIMEOUT=30
